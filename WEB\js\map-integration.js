// Map Integration JavaScript - Global variables
let currentView = 'employees';
let mapInitialized = false;
// Note: employeesData is provided by script.js via window.employeesData

// Global functions for view switching - properly exposed to window object
function switchToEmployees() {
    console.log('Switching to employees view');
    currentView = 'employees';

    const employeeListBtn = document.getElementById('employeeListBtn');
    const officeMapBtn = document.getElementById('officeMapBtn');
    const employeeSection = document.getElementById('employeeSection');
    const mapSection = document.getElementById('mapSection');
    const pageTitle = document.getElementById('pageTitle');

    if (employeeListBtn && officeMapBtn && employeeSection && mapSection && pageTitle) {
        // Update buttons
        employeeListBtn.classList.add('active');
        officeMapBtn.classList.remove('active');

        // Update title
        pageTitle.textContent = 'Seznam zaměstnanců';

        // Switch sections
        mapSection.style.display = 'none';
        employeeSection.style.display = 'block';

        console.log('Successfully switched to employees view');
    } else {
        console.error('Some elements not found for employees view switch');
    }
}

function switchToMap() {
    console.log('Switching to map view');
    currentView = 'map';

    const employeeListBtn = document.getElementById('employeeListBtn');
    const officeMapBtn = document.getElementById('officeMapBtn');
    const employeeSection = document.getElementById('employeeSection');
    const mapSection = document.getElementById('mapSection');
    const pageTitle = document.getElementById('pageTitle');

    if (employeeListBtn && officeMapBtn && employeeSection && mapSection && pageTitle) {
        // Update buttons
        officeMapBtn.classList.add('active');
        employeeListBtn.classList.remove('active');

        // Update title
        pageTitle.textContent = 'Mapa rozmístění pracovišť';

        // Switch sections
        employeeSection.style.display = 'none';
        mapSection.style.display = 'block';

        // Initialize map on first display
        if (!mapInitialized) {
            console.log('Initializing map for first time');
            if (typeof window.initializeMap === 'function') {
                window.initializeMap();
                mapInitialized = true;
            } else {
                console.log('Waiting for map initialization...');
                // Wait for initializeMap to be available
                const checkInitialize = setInterval(() => {
                    if (typeof window.initializeMap === 'function') {
                        clearInterval(checkInitialize);
                        window.initializeMap();
                        mapInitialized = true;
                    }
                }, 50);
            }
        }

        console.log('Successfully switched to map view');
    } else {
        console.error('Some elements not found for map view switch');
    }
}

// Expose functions to global scope for onclick handlers
window.switchToEmployees = switchToEmployees;
window.switchToMap = switchToMap;

document.addEventListener('DOMContentLoaded', function() {
    console.log('Map integration script loaded!');
    console.log('window.employeesData available:', typeof window.employeesData !== 'undefined');
    if (typeof window.employeesData !== 'undefined') {
        console.log('window.employeesData length:', window.employeesData.length);
    }

    // Wait a bit for DOM to fully load
    setTimeout(function() {
        console.log('Initializing map integration...');
        initializeMapIntegration();
    }, 100);
});

function initializeMapIntegration() {
    console.log('initializeMapIntegration started');
    console.log('window.employeesData check:', typeof window.employeesData !== 'undefined');

    // Pozice markerů - kopie z mapa.html
    let markerPositions = [
        {"jmeno": "Smrček Petr", "left": 954, "top": 610},
        {"jmeno": "Mičáň Alex", "left": 891, "top": 579},
        {"jmeno": "Vích Ondřej", "left": 1029, "top": 614},
        {"jmeno": "Tůma Tomáš", "left": 1140, "top": 616},
        {"jmeno": "Hrdá Veronika", "left": 1691, "top": 170},
        {"jmeno": "Vlčková Soňa", "left": 1691, "top": 211},
        {"jmeno": "Hons Jindřich", "left": 1607, "top": 170},
        {"jmeno": "Beáta Barošová", "left": 1560, "top": 169},
        {"jmeno": "Tomek Jiří", "left": 697, "top": 108},
        {"jmeno": "Mareš Jan", "left": 647, "top": 135},
        {"jmeno": "Kohoutová Kateřina", "left": 595, "top": 140},
        {"jmeno": "Ryšavý Miroslav", "left": 595, "top": 104},
        {"jmeno": "Špala Jaroslav", "left": 575, "top": 158},
        {"jmeno": "Rýdl Zdeněk", "left": 522, "top": 121},
        {"jmeno": "Karas Karel", "left": 575, "top": 118},
        {"jmeno": "Hodánek Jaroslav", "left": 804, "top": 116},
        {"jmeno": "Kníže Jaromír", "left": 855, "top": 118},
        {"jmeno": "Pecánek Tomáš", "left": 855, "top": 151},
        {"jmeno": "Bílek Milan", "left": 855, "top": 187},
        {"jmeno": "Dvořák Tomáš", "left": 855, "top": 221},
        {"jmeno": "Kryštof Boháč", "left": 898, "top": 119},
        {"jmeno": "Knop Ondřej", "left": 894, "top": 157},
        {"jmeno": "Gregor Boris", "left": 894, "top": 199},
        {"jmeno": "Nečesaný Jakub", "left": 978, "top": 121},
        {"jmeno": "Srb Václav", "left": 1015, "top": 124},
        {"jmeno": "Šmídek Filip", "left": 1015, "top": 163},
        {"jmeno": "Láník Libor", "left": 1015, "top": 205},
        {"jmeno": "Jedličková Markéta", "left": 1075, "top": 126},
        {"jmeno": "Horová Žaneta", "left": 1075, "top": 164},
        {"jmeno": "Nohejlová Lucie", "left": 1075, "top": 205},
        {"jmeno": "Mesteková Alena", "left": 1135, "top": 126},
        {"jmeno": "Mácová Petra", "left": 1135, "top": 164},
        {"jmeno": "Mnuková Monika", "left": 1135, "top": 205},
        {"jmeno": "Zelenková Jana", "left": 1820, "top": 544},
        {"jmeno": "Jindrová Jiřina", "left": 1752, "top": 572},
        {"jmeno": "Váchalová Zuzana", "left": 1652, "top": 578},
        {"jmeno": "Nardelli Magdalena", "left": 1554, "top": 578},
        {"jmeno": "Soukupová Michaela", "left": 1605, "top": 578},
        {"jmeno": "Raška Michal", "left": 1320, "top": 607},
        {"jmeno": "Tepličanec Pavel", "left": 1212, "top": 612},
        {"jmeno": "Fridrichová Katarína", "left": 1212, "top": 578},
        {"jmeno": "Sojka Alena", "left": 1271, "top": 608},
        {"jmeno": "Záviský Ondřej", "left": 664, "top": 609},
        {"jmeno": "Čermák Martin", "left": 721, "top": 612},
        {"jmeno": "Lebeda Dušan", "left": 721, "top": 577},
        {"jmeno": "Hesko Martin", "left": 791, "top": 573},
        {"jmeno": "Houdek Ondřej", "left": 791, "top": 606},
        {"jmeno": "Novák Petr", "left": 440, "top": 572},
        {"jmeno": "Chemišinec Igor", "left": 400, "top": 612},
        {"jmeno": "Lobotková Alena", "left": 305, "top": 557},
        {"jmeno": "Puchel Michal", "left": 148, "top": 484},
        {"jmeno": "Vichrová Martina", "left": 75, "top": 358},
        {"jmeno": "Drdák Josef", "left": 72, "top": 304},
        {"jmeno": "Kreuzman Jiří", "left": 98, "top": 212},
        {"jmeno": "Kánský Jiří", "left": 1867, "top": 303},
        {"jmeno": "Valent Lajos", "left": 1867, "top": 360},
        {"jmeno": "Procházková Kateřina", "left": 1627, "top": 374},
        {"jmeno": "Kurfiřtová Pavla", "left": 1627, "top": 426},
        {"jmeno": "Bočánek Stanislav", "left": 894, "top": 130},
        {"jmeno": "Radka Maňurová", "left": 1871, "top": 226},
        {"jmeno": "Sylvie Karolová", "left": 1823, "top": 333},
        {"jmeno": "František Černý", "left": 1085, "top": 616}
    ];

    // Load employee data from global variable
    function loadEmployeesData() {
        return new Promise((resolve) => {
            // Wait for data to be loaded from script.js
            if (typeof window.employeesData !== 'undefined' && window.employeesData.length > 0) {
                console.log('Employee data loaded:', window.employeesData.length, 'employees');
                resolve();
            } else {
                // If data is not available, try again after a short delay
                setTimeout(() => {
                    if (typeof window.employeesData !== 'undefined') {
                        console.log('Employee data loaded (delayed):', window.employeesData.length, 'employees');
                    } else {
                        console.warn('Employee data still not available');
                    }
                    resolve();
                }, 100);
            }
        });
    }

    // Funkce pro normalizaci jmen
    function normalizeName(str) {
        return str
            .normalize('NFD')
            .replace(/\p{Diacritic}/gu, '')
            .toLowerCase()
            .replace(/\s+/g, ' ')
            .trim();
    }

    // Detekce pohlaví podle jména - kopie z mapa.html
    function detectGender(fullName) {
        const nameParts = fullName.split(' ');
        const firstName = nameParts[nameParts.length - 1].toLowerCase();

        const femaleNames = [
            'alena', 'andrea', 'beáta', 'beata', 'eva', 'hana', 'helena', 'jana', 'jiřina', 'jirina',
            'kateřina', 'katerina', 'katarína', 'katarina', 'lucie', 'magdalena', 'markéta', 'marketa',
            'martina', 'michaela', 'monika', 'pavla', 'petra', 'soňa', 'sona', 'veronika', 'zuzana',
            'žaneta', 'zaneta', 'alice', 'varvara', 'magda', 'katarína', 'fridrichová', 'staňková',
            'stašková', 'soukupová', 'procházková', 'kurfiřtová', 'vachalová', 'erhartová', 'hulová',
            'jedličková', 'horová', 'nohejlová', 'mesteková', 'mácová', 'mnuková', 'nardelli', 'zelenková',
            'zezuľáková', 'karolová', 'maňurová', 'vichrová', 'vlčková', 'hrdá', 'gabriel', 'kopecká',
            'sojka', 'pešková'
        ];

        const hasFemaleLastName = fullName.toLowerCase().includes('ová') ||
            (fullName.toLowerCase().endsWith('ina') && !fullName.toLowerCase().includes('ák') && !fullName.toLowerCase().includes('ak'));

        const specialFemaleNames = [
            'vasjuňkina varvara', 'hrdá veronika', 'gabriel martina', 'kopecká zuzana',
            'nardelli magdalena', 'sojka alena', 'pešková monika'
        ];

        const isSpecialFemale = specialFemaleNames.includes(fullName.toLowerCase());

        const maleNames = [
            'petr', 'tomáš', 'tomas', 'ondřej', 'ondrej', 'jakub', 'jindřich', 'jindrich', 'jaroslav',
            'boris', 'libor', 'václav', 'vaclav', 'filip', 'vojtěch', 'vojtech', 'pavel', 'michal',
            'karel', 'jaromír', 'jaromir', 'roman', 'miroslav', 'zdeněk', 'zdenek', 'dušan', 'dusan',
            'josef', 'jiří', 'jiri', 'stanislav', 'igor', 'akaki', 'zbyněk', 'zbyněk', 'kryštof',
            'krystof', 'martin', 'ondřej', 'ondrej', 'pavel', 'lajos', 'františek', 'frantisek'
        ];

        const isMale = maleNames.includes(firstName);
        const isFemale = femaleNames.includes(firstName) || hasFemaleLastName || isSpecialFemale;

        if (isMale) {
            return 'male';
        } else if (isFemale) {
            return 'female';
        } else {
            return 'male'; // default
        }
    }

    // Načtení pozic markerů z mapa.html
    function loadMarkerPositions() {
        markerPositions = [
            {"jmeno": "Smrček Petr", "left": 954, "top": 610},
            {"jmeno": "Mičáň Alex", "left": 891, "top": 579},
            {"jmeno": "Vích Ondřej", "left": 1029, "top": 614},
            {"jmeno": "Tůma Tomáš", "left": 1140, "top": 616},
            {"jmeno": "Hrdá Veronika", "left": 1691, "top": 170},
            {"jmeno": "Vlčková Soňa", "left": 1691, "top": 211},
            {"jmeno": "Hons Jindřich", "left": 1607, "top": 170},
            {"jmeno": "Beáta Barošová", "left": 1560, "top": 169},
            {"jmeno": "Tomek Jiří", "left": 697, "top": 108},
            {"jmeno": "Zezuláková Andrea", "left": 697, "top": 143},
            {"jmeno": "Brzobohatá Jana", "left": 697, "top": 180},
            {"jmeno": "Haufenhoferová Eva", "left": 697, "top": 213},
            {"jmeno": "Kalábová Lucie", "left": 730, "top": 107},
            {"jmeno": "Mašková Hana", "left": 730, "top": 141},
            {"jmeno": "Prihara Roman", "left": 730, "top": 180},
            {"jmeno": "Laco Dušan", "left": 359, "top": 99},
            {"jmeno": "Šrom Jakub", "left": 461, "top": 114},
            {"jmeno": "Sharashenidze Akaki", "left": 647, "top": 173},
            {"jmeno": "Bok Zbyněk", "left": 647, "top": 100},
            {"jmeno": "Mareš Jan", "left": 647, "top": 135},
            {"jmeno": "Kohoutová Kateřina", "left": 595, "top": 140},
            {"jmeno": "Ryšavý Miroslav", "left": 595, "top": 104},
            {"jmeno": "Špala Jaroslav", "left": 575, "top": 158},
            {"jmeno": "Rýdl Zdeněk", "left": 522, "top": 121},
            {"jmeno": "Karas Karel", "left": 575, "top": 118},
            {"jmeno": "Hodánek Jaroslav", "left": 804, "top": 116},
            {"jmeno": "Kníže Jaromír", "left": 855, "top": 118},
            {"jmeno": "Pecánek Tomáš", "left": 855, "top": 151},
            {"jmeno": "Bílek Milan", "left": 855, "top": 187},
            {"jmeno": "Dvořák Tomáš", "left": 855, "top": 221},
            {"jmeno": "Kryštof Boháč", "left": 898, "top": 119},
            {"jmeno": "Knop Ondřej", "left": 894, "top": 157},
            {"jmeno": "Gregor Boris", "left": 894, "top": 199},
            {"jmeno": "Nečesaný Jakub", "left": 978, "top": 121},
            {"jmeno": "Srb Václav", "left": 1015, "top": 124},
            {"jmeno": "Šmídek Filip", "left": 1015, "top": 163},
            {"jmeno": "Láník Libor", "left": 1015, "top": 205},
            {"jmeno": "Jedličková Markéta", "left": 1075, "top": 124}
        ];
    }



    // Inicializace mapy - přesná kopie z mapa.html
    function initializeMap() {
        console.log('Inicializace mapy...');
        loadMarkerPositions();
        renderEmployeeList(window.employeesData);
        renderMarkers(window.employeesData);
        setupMapSearch();
        setupMapToggle();

        // Kontrola načtení obrázku
        const mapImg = document.getElementById('office-map-img');
        const errorDiv = document.getElementById('map-error');

        mapImg.onload = function() {
            errorDiv.style.display = 'none';
        };

        mapImg.onerror = function() {
            errorDiv.style.display = 'block';
        };
    }

    // Make initializeMap globally accessible
    window.initializeMap = initializeMap;

    // Vytvoření horizontálního seznamu zaměstnanců - přesná kopie z mapa.html
    function renderEmployeeList(list) {
        const ul = document.getElementById('mapEmployeeList');
        if (!ul) {
            console.error('Element mapEmployeeList nebyl nalezen');
            return;
        }

        console.log('Rendering employee list, počet:', list.length);

        // Fade out effect
        ul.style.opacity = '0.5';
        ul.style.transform = 'translateY(10px)';

        setTimeout(() => {
            ul.innerHTML = '';

            console.log('Vytvářím elementy pro', list.length, 'zaměstnanců');
            list.forEach((z, index) => {
                const li = document.createElement('li');
                li.tabIndex = 0;
                li.dataset.jmeno = z.jmeno;

                const avatar = document.createElement('img');
                avatar.src = z.obrazek || 'img/no-person-photo.png';
                avatar.alt = z.jmeno;
                avatar.className = 'avatar-img';
                avatar.onerror = () => { avatar.src = 'img/no-person-photo.png'; };
                li.appendChild(avatar);

                const infoContainer = document.createElement('div');
                infoContainer.className = 'employee-info';

                const nameElement = document.createElement('h3');
                const nameParts = z.jmeno.split(' ');
                if (nameParts.length >= 2) {
                    nameElement.innerHTML = nameParts[0] + '<br>' + nameParts.slice(1).join(' ');
                } else {
                    nameElement.textContent = z.jmeno;
                }
                nameElement.className = 'emp-name';
                nameElement.title = z.jmeno;
                infoContainer.appendChild(nameElement);

                li.appendChild(infoContainer);

                const btn = document.createElement('button');
                btn.innerHTML = '<i class="fas fa-map-marker-alt"></i> Najít';
                btn.className = 'find-btn';
                btn.title = 'Zobrazit na mapě';
                btn.onclick = e => { e.stopPropagation(); highlightByName(z.jmeno, true); };
                li.appendChild(btn);

                li.onclick = e => {
                    e.preventDefault();
                    highlightByName(z.jmeno, true);
                };

                li.addEventListener('keydown', e => {
                    if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        highlightByName(z.jmeno, true);
                    }
                });

                li.style.opacity = '0';
                li.style.transform = 'translateX(-20px)';
                ul.appendChild(li);
                console.log('Přidán element pro:', z.jmeno);

                setTimeout(() => {
                    li.style.transition = 'all 0.3s ease';
                    li.style.opacity = '1';
                    li.style.transform = 'translateX(0)';
                }, index * 50);
            });

            setTimeout(() => {
                ul.style.transition = 'all 0.3s ease';
                ul.style.opacity = '1';
                ul.style.transform = 'translateY(0)';
                console.log('Seznam zaměstnanců vykreslen, počet elementů:', ul.children.length);
            }, 100);
        }, 150);
    }

    // Vytvoření markerů na mapě - přesná kopie z mapa.html
    function renderMarkers(list) {
        const container = document.querySelector('.office-map-container');
        if (!container) {
            console.error('Map container not found');
            return;
        }

        container.querySelectorAll('.marker').forEach(m => m.remove());

        markerPositions.forEach((pos, i) => {
            const idx = list.findIndex(z => z.jmeno === pos.jmeno);
            if (idx === -1) return;
            const marker = document.createElement('div');

            const isFemaleName = detectGender(pos.jmeno);
            marker.className = `marker ${isFemaleName ? 'female' : 'male'}`;

            marker.style.top = pos.top + 'px';
            marker.style.left = pos.left + 'px';
            marker.dataset.jmeno = pos.jmeno;
            marker.tabIndex = 0;

            const genderDot = document.createElement('div');
            genderDot.style.width = '26px';
            genderDot.style.height = '26px';
            genderDot.style.borderRadius = '50%';
            genderDot.style.background = isFemaleName ? '#ec4899' : '#3b82f6';
            genderDot.style.boxShadow = isFemaleName ?
                '0 2px 8px rgba(236, 72, 153, 0.3)' :
                '0 2px 8px rgba(59, 130, 246, 0.3)';
            genderDot.style.border = '2.5px solid #fff';
            genderDot.style.display = 'block';
            genderDot.style.transition = 'all 0.3s ease';
            genderDot.title = `${pos.jmeno} (${isFemaleName ? 'Žena' : 'Muž'})`;
            marker.appendChild(genderDot);

            const tooltip = document.createElement('span');
            tooltip.className = 'tooltip';
            tooltip.innerHTML = pos.jmeno;
            marker.appendChild(tooltip);

            marker.addEventListener('click', () => {
                highlightByName(pos.jmeno, true);
            });

            container.appendChild(marker);
        });
    }

    // Zvýraznění podle jména - přesná kopie z mapa.html
    function highlightByName(jmeno, scrollTo) {
        // Vyčištění všech zvýraznění
        clearAllHighlights();

        // Najít a zvýraznit marker
        const activeMarker = findAndHighlightMarker(jmeno, scrollTo);

        // Najít a zvýraznit zaměstnance
        const activeEmployee = findAndHighlightEmployee(jmeno, scrollTo);

        // Zobrazit animaci nalezení
        if (activeEmployee || activeMarker) {
            showFoundAnimation(jmeno);
        }
    }

    // Vyčištění všech zvýraznění
    function clearAllHighlights() {
        document.querySelectorAll('.marker').forEach(marker => {
            marker.classList.remove('active', 'found-pulse');
        });
        document.querySelectorAll('#mapEmployeeList li').forEach(li => {
            li.classList.remove('active', 'found-highlight');
        });
    }

    // Najít a zvýraznit marker
    function findAndHighlightMarker(jmeno, scrollTo) {
        let foundMarker = null;
        document.querySelectorAll('.marker').forEach(marker => {
            const isActive = marker.dataset.jmeno === jmeno;
            if (isActive) {
                foundMarker = marker;
                marker.classList.add('active', 'found-pulse');

                if (scrollTo) {
                    setTimeout(() => {
                        marker.scrollIntoView({
                            behavior: 'smooth',
                            block: 'center',
                            inline: 'center'
                        });

                        setTimeout(() => {
                            marker.classList.add('extra-pulse');
                            setTimeout(() => marker.classList.remove('extra-pulse'), 2000);
                        }, 800);
                    }, 300);
                }
            }
        });
        return foundMarker;
    }

    // Najít a zvýraznit zaměstnance v seznamu
    function findAndHighlightEmployee(jmeno, scrollTo) {
        let foundEmployee = null;
        document.querySelectorAll('#mapEmployeeList li').forEach(li => {
            const isActive = li.dataset.jmeno === jmeno;
            if (isActive) {
                foundEmployee = li;
                li.classList.add('active', 'found-highlight');

                if (scrollTo) {
                    setTimeout(() => {
                        li.scrollIntoView({
                            behavior: 'smooth',
                            block: 'nearest',
                            inline: 'nearest'
                        });
                    }, 100);
                }
            }
        });
        return foundEmployee;
    }

    // Animace nalezení
    function showFoundAnimation(jmeno) {
        console.log('Nalezen:', jmeno);
        // Zde můžeme přidat další animace podle potřeby
    }

    // Vyhledávání na mapě - přesná kopie z mapa.html
    function setupMapSearch() {
        const searchInput = document.getElementById('mapSearchInput');
        const searchClearBtn = document.getElementById('mapSearchClearBtn');
        let searchTimeout;

        if (!searchInput || !searchClearBtn) {
            console.log('Search elements not found');
            return;
        }

        function updateClearButton() {
            if (searchInput.value.trim()) {
                searchClearBtn.classList.add('visible');
            } else {
                searchClearBtn.classList.remove('visible');
            }
        }

        function updateResultsCount(count, isSearching) {
            const resultsCount = document.getElementById('mapSearchResultsCount');
            const resultsText = document.getElementById('mapResultsCountText');

            if (resultsCount && resultsText) {
                if (isSearching) {
                    resultsText.textContent = `${count} výsledků`;
                    resultsCount.classList.add('visible');
                } else {
                    resultsCount.classList.remove('visible');
                }
            }
        }

        searchInput.addEventListener('input', function() {
            const val = this.value.toLowerCase().trim();
            updateClearButton();

            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                if (val === '') {
                    renderEmployeeList(window.employeesData);
                    updateResultsCount(window.employeesData.length, false);
                } else {
                    const filtered = window.employeesData.filter(z => {
                        const name = normalizeName(z.jmeno);
                        const searchTerm = normalizeName(val);
                        return name.includes(searchTerm);
                    });

                    renderEmployeeList(filtered);
                    updateResultsCount(filtered.length, true);

                    if (filtered.length === 1) {
                        setTimeout(() => {
                            highlightByName(filtered[0].jmeno, true);
                        }, 500);
                    }
                }
            }, 200);
        });

        searchClearBtn.addEventListener('click', function() {
            searchInput.value = '';
            updateClearButton();
            renderEmployeeList(window.employeesData);
            updateResultsCount(window.employeesData.length, false);
            searchInput.focus();
        });
    }



    // Nastavení toggle tlačítek pro zobrazení - kopie z mapa.html
    function setupMapToggle() {
        const toggleBtns = document.querySelectorAll('.toggle-btn');
        const mapContainer = document.querySelector('.office-map-container');

        if (!toggleBtns.length || !mapContainer) {
            console.log('Toggle buttons or map container not found');
            return;
        }

        toggleBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const view = this.getAttribute('data-view');

                // Aktualizace aktivního tlačítka
                toggleBtns.forEach(b => b.classList.remove('active'));
                this.classList.add('active');

                // Přepnutí režimu zobrazení
                if (view === 'single') {
                    mapContainer.classList.add('single-view');
                } else {
                    mapContainer.classList.remove('single-view');
                }

                console.log('Přepnuto na režim:', view);
            });
        });
    }

    // Globální funkce pro vyhledání zaměstnance na mapě - přesná kopie z mapa.html
    window.findEmployeeOnMap = function(employeeName) {
        if (currentView !== 'map') {
            switchToMap();
            setTimeout(() => findEmployeeOnMap(employeeName), 500);
            return;
        }

        highlightByName(employeeName, true);
    };

    // Zobrazení detailů zaměstnance (použije existující modal)
    function showEmployeeDetails(employee) {
        // Použije existující modal z hlavní stránky
        if (typeof window.openModal === 'function') {
            window.openModal(employee);
        }
    }

    // Navigační tlačítka nyní používají onclick atributy

    // Inicializace
    loadEmployeesData().then(() => {
        console.log('Data načtena, inicializace dokončena');
        // Nastavení výchozího zobrazení
        window.switchToEmployees();
    });
}
