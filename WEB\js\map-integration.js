// Map Integration JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Elementy pro navigaci
    const employeeListBtn = document.getElementById('employeeListBtn');
    const officeMapBtn = document.getElementById('officeMapBtn');
    const employeeSection = document.getElementById('employeeSection');
    const mapSection = document.getElementById('mapSection');
    const pageTitle = document.getElementById('pageTitle');

    // Inicializace
    let currentView = 'employees';
    let employeesData = [];
    let mapInitialized = false;

    // Pozice markerů - kopie z mapa.html
    let markerPositions = [
        {"jmeno": "Smr<PERSON><PERSON>", "left": 954, "top": 610},
        {"jmeno": "Mič<PERSON>ň <PERSON>", "left": 891, "top": 579},
        {"jmeno": "<PERSON><PERSON><PERSON>", "left": 1029, "top": 614},
        {"jmeno": "<PERSON><PERSON><PERSON>", "left": 1140, "top": 616},
        {"jmeno": "<PERSON>rd<PERSON> Veronika", "left": 1691, "top": 170},
        {"jmeno": "Vl<PERSON>kov<PERSON> Soňa", "left": 1691, "top": 211},
        {"jmeno": "Hons Jindřich", "left": 1607, "top": 170},
        {"jmeno": "Be<PERSON>ta Barošová", "left": 1560, "top": 169},
        {"jmeno": "Tomek Jiří", "left": 697, "top": 108},
        {"jmeno": "Mareš Jan", "left": 647, "top": 135},
        {"jmeno": "Kohoutová Kateřina", "left": 595, "top": 140},
        {"jmeno": "Ryšavý Miroslav", "left": 595, "top": 104},
        {"jmeno": "Špala Jaroslav", "left": 575, "top": 158},
        {"jmeno": "Rýdl Zdeněk", "left": 522, "top": 121},
        {"jmeno": "Karas Karel", "left": 575, "top": 118},
        {"jmeno": "Hodánek Jaroslav", "left": 804, "top": 116},
        {"jmeno": "Kníže Jaromír", "left": 855, "top": 118},
        {"jmeno": "Pecánek Tomáš", "left": 855, "top": 151},
        {"jmeno": "Bílek Milan", "left": 855, "top": 187},
        {"jmeno": "Dvořák Tomáš", "left": 855, "top": 221},
        {"jmeno": "Kryštof Boháč", "left": 898, "top": 119},
        {"jmeno": "Knop Ondřej", "left": 894, "top": 157},
        {"jmeno": "Gregor Boris", "left": 894, "top": 199},
        {"jmeno": "Nečesaný Jakub", "left": 978, "top": 121},
        {"jmeno": "Srb Václav", "left": 1015, "top": 124},
        {"jmeno": "Šmídek Filip", "left": 1015, "top": 163},
        {"jmeno": "Láník Libor", "left": 1015, "top": 205},
        {"jmeno": "Jedličková Markéta", "left": 1075, "top": 126},
        {"jmeno": "Horová Žaneta", "left": 1075, "top": 164},
        {"jmeno": "Nohejlová Lucie", "left": 1075, "top": 205},
        {"jmeno": "Mesteková Alena", "left": 1135, "top": 126},
        {"jmeno": "Mácová Petra", "left": 1135, "top": 164},
        {"jmeno": "Mnuková Monika", "left": 1135, "top": 205},
        {"jmeno": "Zelenková Jana", "left": 1820, "top": 544},
        {"jmeno": "Jindrová Jiřina", "left": 1752, "top": 572},
        {"jmeno": "Váchalová Zuzana", "left": 1652, "top": 578},
        {"jmeno": "Nardelli Magdalena", "left": 1554, "top": 578},
        {"jmeno": "Soukupová Michaela", "left": 1605, "top": 578},
        {"jmeno": "Raška Michal", "left": 1320, "top": 607},
        {"jmeno": "Tepličanec Pavel", "left": 1212, "top": 612},
        {"jmeno": "Fridrichová Katarína", "left": 1212, "top": 578},
        {"jmeno": "Sojka Alena", "left": 1271, "top": 608},
        {"jmeno": "Záviský Ondřej", "left": 664, "top": 609},
        {"jmeno": "Čermák Martin", "left": 721, "top": 612},
        {"jmeno": "Lebeda Dušan", "left": 721, "top": 577},
        {"jmeno": "Hesko Martin", "left": 791, "top": 573},
        {"jmeno": "Houdek Ondřej", "left": 791, "top": 606},
        {"jmeno": "Novák Petr", "left": 440, "top": 572},
        {"jmeno": "Chemišinec Igor", "left": 400, "top": 612},
        {"jmeno": "Lobotková Alena", "left": 305, "top": 557},
        {"jmeno": "Puchel Michal", "left": 148, "top": 484},
        {"jmeno": "Vichrová Martina", "left": 75, "top": 358},
        {"jmeno": "Drdák Josef", "left": 72, "top": 304},
        {"jmeno": "Kreuzman Jiří", "left": 98, "top": 212},
        {"jmeno": "Kánský Jiří", "left": 1867, "top": 303},
        {"jmeno": "Valent Lajos", "left": 1867, "top": 360},
        {"jmeno": "Procházková Kateřina", "left": 1627, "top": 374},
        {"jmeno": "Kurfiřtová Pavla", "left": 1627, "top": 426},
        {"jmeno": "Bočánek Stanislav", "left": 894, "top": 130},
        {"jmeno": "Radka Maňurová", "left": 1871, "top": 226},
        {"jmeno": "Sylvie Karolová", "left": 1823, "top": 333},
        {"jmeno": "František Černý", "left": 1085, "top": 616}
    ];

    // Načtení dat zaměstnanců z globální proměnné
    function loadEmployeesData() {
        return new Promise((resolve) => {
            // Čekáme na načtení dat z script.js
            if (typeof window.employeesData !== 'undefined' && window.employeesData.length > 0) {
                employeesData = window.employeesData;
                console.log('Načtena data zaměstnanců:', employeesData.length);
                resolve();
            } else {
                // Pokud data nejsou k dispozici, zkusíme to za chvíli
                setTimeout(() => {
                    if (typeof window.employeesData !== 'undefined') {
                        employeesData = window.employeesData;
                        console.log('Načtena data zaměstnanců (opožděně):', employeesData.length);
                    }
                    resolve();
                }, 100);
            }
        });
    }

    // Funkce pro normalizaci jmen
    function normalizeName(str) {
        return str
            .normalize('NFD')
            .replace(/\p{Diacritic}/gu, '')
            .toLowerCase()
            .replace(/\s+/g, ' ')
            .trim();
    }

    // Detekce pohlaví podle jména - kopie z mapa.html
    function detectGender(fullName) {
        const nameParts = fullName.split(' ');
        const firstName = nameParts[nameParts.length - 1].toLowerCase();

        const femaleNames = [
            'alena', 'andrea', 'beáta', 'beata', 'eva', 'hana', 'helena', 'jana', 'jiřina', 'jirina',
            'kateřina', 'katerina', 'katarína', 'katarina', 'lucie', 'magdalena', 'markéta', 'marketa',
            'martina', 'michaela', 'monika', 'pavla', 'petra', 'soňa', 'sona', 'veronika', 'zuzana',
            'žaneta', 'zaneta', 'alice', 'varvara', 'magda', 'katarína', 'fridrichová', 'staňková',
            'stašková', 'soukupová', 'procházková', 'kurfiřtová', 'vachalová', 'erhartová', 'hulová',
            'jedličková', 'horová', 'nohejlová', 'mesteková', 'mácová', 'mnuková', 'nardelli', 'zelenková',
            'zezuľáková', 'karolová', 'maňurová', 'vichrová', 'vlčková', 'hrdá', 'gabriel', 'kopecká',
            'sojka', 'pešková'
        ];

        const hasFemaleLastName = fullName.toLowerCase().includes('ová') ||
            (fullName.toLowerCase().endsWith('ina') && !fullName.toLowerCase().includes('ák') && !fullName.toLowerCase().includes('ak'));

        const specialFemaleNames = [
            'vasjuňkina varvara', 'hrdá veronika', 'gabriel martina', 'kopecká zuzana',
            'nardelli magdalena', 'sojka alena', 'pešková monika'
        ];

        const isSpecialFemale = specialFemaleNames.includes(fullName.toLowerCase());

        const maleNames = [
            'petr', 'tomáš', 'tomas', 'ondřej', 'ondrej', 'jakub', 'jindřich', 'jindrich', 'jaroslav',
            'boris', 'libor', 'václav', 'vaclav', 'filip', 'vojtěch', 'vojtech', 'pavel', 'michal',
            'karel', 'jaromír', 'jaromir', 'roman', 'miroslav', 'zdeněk', 'zdenek', 'dušan', 'dusan',
            'josef', 'jiří', 'jiri', 'stanislav', 'igor', 'akaki', 'zbyněk', 'zbyněk', 'kryštof',
            'krystof', 'martin', 'ondřej', 'ondrej', 'pavel', 'lajos', 'františek', 'frantisek'
        ];

        const isMale = maleNames.includes(firstName);
        const isFemale = femaleNames.includes(firstName) || hasFemaleLastName || isSpecialFemale;

        if (isMale) {
            return 'male';
        } else if (isFemale) {
            return 'female';
        } else {
            return 'male'; // default
        }
    }

    // Načtení pozic markerů z mapa.html
    function loadMarkerPositions() {
        markerPositions = [
            {"jmeno": "Smrček Petr", "left": 954, "top": 610},
            {"jmeno": "Mičáň Alex", "left": 891, "top": 579},
            {"jmeno": "Vích Ondřej", "left": 1029, "top": 614},
            {"jmeno": "Tůma Tomáš", "left": 1140, "top": 616},
            {"jmeno": "Hrdá Veronika", "left": 1691, "top": 170},
            {"jmeno": "Vlčková Soňa", "left": 1691, "top": 211},
            {"jmeno": "Hons Jindřich", "left": 1607, "top": 170},
            {"jmeno": "Beáta Barošová", "left": 1560, "top": 169},
            {"jmeno": "Tomek Jiří", "left": 697, "top": 108},
            {"jmeno": "Zezuláková Andrea", "left": 697, "top": 143},
            {"jmeno": "Brzobohatá Jana", "left": 697, "top": 180},
            {"jmeno": "Haufenhoferová Eva", "left": 697, "top": 213},
            {"jmeno": "Kalábová Lucie", "left": 730, "top": 107},
            {"jmeno": "Mašková Hana", "left": 730, "top": 141},
            {"jmeno": "Prihara Roman", "left": 730, "top": 180},
            {"jmeno": "Laco Dušan", "left": 359, "top": 99},
            {"jmeno": "Šrom Jakub", "left": 461, "top": 114},
            {"jmeno": "Sharashenidze Akaki", "left": 647, "top": 173},
            {"jmeno": "Bok Zbyněk", "left": 647, "top": 100},
            {"jmeno": "Mareš Jan", "left": 647, "top": 135},
            {"jmeno": "Kohoutová Kateřina", "left": 595, "top": 140},
            {"jmeno": "Ryšavý Miroslav", "left": 595, "top": 104},
            {"jmeno": "Špala Jaroslav", "left": 575, "top": 158},
            {"jmeno": "Rýdl Zdeněk", "left": 522, "top": 121},
            {"jmeno": "Karas Karel", "left": 575, "top": 118},
            {"jmeno": "Hodánek Jaroslav", "left": 804, "top": 116},
            {"jmeno": "Kníže Jaromír", "left": 855, "top": 118},
            {"jmeno": "Pecánek Tomáš", "left": 855, "top": 151},
            {"jmeno": "Bílek Milan", "left": 855, "top": 187},
            {"jmeno": "Dvořák Tomáš", "left": 855, "top": 221},
            {"jmeno": "Kryštof Boháč", "left": 898, "top": 119},
            {"jmeno": "Knop Ondřej", "left": 894, "top": 157},
            {"jmeno": "Gregor Boris", "left": 894, "top": 199},
            {"jmeno": "Nečesaný Jakub", "left": 978, "top": 121},
            {"jmeno": "Srb Václav", "left": 1015, "top": 124},
            {"jmeno": "Šmídek Filip", "left": 1015, "top": 163},
            {"jmeno": "Láník Libor", "left": 1015, "top": 205},
            {"jmeno": "Jedličková Markéta", "left": 1075, "top": 124}
        ];
    }

    // Přepínání mezi sekcemi
    function switchToEmployees() {
        currentView = 'employees';

        // Aktualizace tlačítek
        employeeListBtn.classList.add('active');
        officeMapBtn.classList.remove('active');

        // Aktualizace titulku
        pageTitle.textContent = 'Seznam zaměstnanců';

        // Přepnutí sekcí
        mapSection.style.display = 'none';
        employeeSection.style.display = 'block';
    }

    function switchToMap() {
        currentView = 'map';

        // Aktualizace tlačítek
        officeMapBtn.classList.add('active');
        employeeListBtn.classList.remove('active');

        // Aktualizace titulku
        pageTitle.textContent = 'Mapa rozmístění pracovišť';

        // Přepnutí sekcí
        employeeSection.style.display = 'none';
        mapSection.style.display = 'block';

        // Inicializace mapy při prvním zobrazení
        if (!mapInitialized) {
            initializeMap();
            mapInitialized = true;
        }
    }

    // Inicializace mapy
    function initializeMap() {
        console.log('Inicializace mapy...');
        loadMarkerPositions();
        createEmployeeList();
        createMarkers();
        setupMapSearch();
        setupMapToggle();

        // Kontrola načtení obrázku
        const mapImg = document.getElementById('office-map-img');
        const errorDiv = document.getElementById('map-error');

        mapImg.onload = function() {
            errorDiv.style.display = 'none';
        };

        mapImg.onerror = function() {
            errorDiv.style.display = 'block';
        };
    }

    // Vytvoření horizontálního seznamu zaměstnanců - kopie z mapa.html
    function createEmployeeList() {
        const employeeList = document.getElementById('mapEmployeeList');
        if (!employeeList) return;

        employeeList.innerHTML = '';

        // Filtrujeme pouze zaměstnance, kteří mají pozici na mapě
        const employeesWithPositions = employeesData.filter(emp =>
            markerPositions.some(pos => normalizeName(pos.jmeno) === normalizeName(emp.jmeno))
        );

        console.log('Zaměstnanci s pozicemi:', employeesWithPositions.length);

        employeesWithPositions.forEach(employee => {
            const li = document.createElement('li');
            li.setAttribute('data-employee', employee.jmeno);
            li.innerHTML = `
                <img src="${employee.obrazek || 'img/no-person-photo.png'}" alt="${employee.jmeno}" class="avatar-img">
                <div class="employee-info">
                    <span class="emp-name">${employee.jmeno}</span>
                </div>
                <button class="find-btn" onclick="findEmployeeOnMap('${employee.jmeno}')">
                    <i class="fas fa-search"></i>
                    Najít
                </button>
            `;

            employeeList.appendChild(li);
        });
    }

    // Vytvoření markerů na mapě - kopie z mapa.html
    function createMarkers() {
        const mapContainer = document.getElementById('mapContainer');
        if (!mapContainer) return;

        // Odstranění existujících markerů
        const existingMarkers = mapContainer.querySelectorAll('.marker');
        existingMarkers.forEach(marker => marker.remove());

        console.log('Vytváření markerů pro', markerPositions.length, 'pozic');

        markerPositions.forEach(position => {
            const employee = employeesData.find(emp => normalizeName(emp.jmeno) === normalizeName(position.jmeno));
            if (!employee) {
                console.log('Nenalezen zaměstnanec pro pozici:', position.jmeno);
                return;
            }

            const marker = document.createElement('div');
            const gender = detectGender(employee.jmeno);
            marker.className = `marker ${gender}`;
            marker.style.left = position.left + 'px';
            marker.style.top = position.top + 'px';
            marker.setAttribute('data-employee', employee.jmeno);

            marker.innerHTML = `
                <img src="${employee.obrazek || 'img/no-person-photo.png'}" alt="${employee.jmeno}" class="marker-avatar">
                <div class="tooltip">${employee.jmeno}</div>
            `;

            // Event listener pro kliknutí na marker
            marker.addEventListener('click', () => {
                highlightEmployeeOnMap(employee.jmeno);
                showEmployeeDetails(employee);
            });

            mapContainer.appendChild(marker);
        });
    }

    // Zvýraznění zaměstnance na mapě - kopie z mapa.html
    function highlightEmployeeOnMap(employeeName) {
        // Odstranění předchozího zvýraznění
        document.querySelectorAll('#mapEmployeeList li').forEach(li => li.classList.remove('active'));
        document.querySelectorAll('.marker').forEach(marker => {
            marker.classList.remove('active', 'spotlight');
        });

        // Zvýraznění v seznamu
        const employeeItem = document.querySelector(`#mapEmployeeList li[data-employee="${employeeName}"]`);
        if (employeeItem) {
            employeeItem.classList.add('active');
            employeeItem.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }

        // Zvýraznění markeru
        const marker = document.querySelector(`.marker[data-employee="${employeeName}"]`);
        if (marker) {
            marker.classList.add('active', 'spotlight');
        }
    }

    // Vyhledávání na mapě - kopie z mapa.html
    function setupMapSearch() {
        const searchInput = document.getElementById('mapSearchInput');
        const clearBtn = document.getElementById('mapSearchClearBtn');
        const resultsCount = document.getElementById('mapSearchResultsCount');

        if (!searchInput || !clearBtn) return;

        searchInput.addEventListener('input', function() {
            const query = this.value.toLowerCase().trim();

            if (query.length > 0) {
                clearBtn.classList.add('visible');
                const count = filterEmployeesOnMap(query);
                if (resultsCount) {
                    const resultsText = document.getElementById('mapResultsCountText');
                    if (resultsText) {
                        resultsText.textContent = `${count} výsledků`;
                    }
                    resultsCount.classList.add('visible');
                }
            } else {
                clearBtn.classList.remove('visible');
                showAllEmployeesOnMap();
                if (resultsCount) {
                    resultsCount.classList.remove('visible');
                }
            }
        });

        clearBtn.addEventListener('click', function() {
            searchInput.value = '';
            clearBtn.classList.remove('visible');
            showAllEmployeesOnMap();
            if (resultsCount) {
                resultsCount.classList.remove('visible');
            }
            searchInput.focus();
        });
    }

    // Filtrování zaměstnanců na mapě - kopie z mapa.html
    function filterEmployeesOnMap(query) {
        const employeeItems = document.querySelectorAll('#mapEmployeeList li');
        const markers = document.querySelectorAll('.marker');
        let visibleCount = 0;

        employeeItems.forEach(item => {
            const name = item.querySelector('.emp-name').textContent.toLowerCase();
            const employeeName = item.getAttribute('data-employee');
            const marker = document.querySelector(`.marker[data-employee="${employeeName}"]`);

            if (normalizeName(name).includes(normalizeName(query))) {
                item.style.display = 'flex';
                if (marker) marker.style.display = 'flex';
                visibleCount++;
            } else {
                item.style.display = 'none';
                if (marker) marker.style.display = 'none';
            }
        });

        console.log(`Nalezeno ${visibleCount} výsledků pro "${query}"`);
        return visibleCount;
    }

    // Zobrazení všech zaměstnanců na mapě
    function showAllEmployeesOnMap() {
        const employeeItems = document.querySelectorAll('#mapEmployeeList li');
        const markers = document.querySelectorAll('.marker');

        employeeItems.forEach(item => item.style.display = 'flex');
        markers.forEach(marker => marker.style.display = 'flex');
    }

    // Nastavení toggle tlačítek pro zobrazení - kopie z mapa.html
    function setupMapToggle() {
        const toggleBtns = document.querySelectorAll('.toggle-btn');
        const mapContainer = document.getElementById('mapContainer');

        if (!toggleBtns.length || !mapContainer) return;

        toggleBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const view = this.getAttribute('data-view');

                // Aktualizace aktivního tlačítka
                toggleBtns.forEach(b => b.classList.remove('active'));
                this.classList.add('active');

                // Přepnutí režimu zobrazení
                if (view === 'single') {
                    mapContainer.classList.add('single-view');
                } else {
                    mapContainer.classList.remove('single-view');
                }

                console.log('Přepnuto na režim:', view);
            });
        });
    }

    // Globální funkce pro vyhledání zaměstnance na mapě - kopie z mapa.html
    window.findEmployeeOnMap = function(employeeName) {
        if (currentView !== 'map') {
            switchToMap();
            setTimeout(() => findEmployeeOnMap(employeeName), 500);
            return;
        }

        highlightEmployeeOnMap(employeeName);

        const employee = employeesData.find(emp => normalizeName(emp.jmeno) === normalizeName(employeeName));
        if (employee) {
            showEmployeeDetails(employee);
        }
    };

    // Zobrazení detailů zaměstnance (použije existující modal)
    function showEmployeeDetails(employee) {
        // Použije existující modal z hlavní stránky
        if (typeof window.openModal === 'function') {
            window.openModal(employee);
        }
    }

    // Event listenery pro navigační tlačítka
    if (employeeListBtn && officeMapBtn) {
        employeeListBtn.addEventListener('click', (e) => {
            e.preventDefault();
            switchToEmployees();
        });

        officeMapBtn.addEventListener('click', (e) => {
            e.preventDefault();
            switchToMap();
        });
    }

    // Inicializace
    loadEmployeesData().then(() => {
        console.log('Data načtena, inicializace dokončena');
        // Nastavení výchozího zobrazení
        switchToEmployees();
    });
});
