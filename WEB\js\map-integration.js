// Map Integration JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Elementy pro navigaci
    const employeeListBtn = document.getElementById('employeeListBtn');
    const officeMapBtn = document.getElementById('officeMapBtn');
    const employeeSection = document.getElementById('employeeSection');
    const mapSection = document.getElementById('mapSection');
    const pageTitle = document.getElementById('pageTitle');

    // Inicializace
    let currentView = 'employees';
    let employeesData = [];
    let markerPositions = [];

    // Načtení dat zaměstnanců z globální proměnné
    function loadEmployeesData() {
        return new Promise((resolve) => {
            // Čekáme na načtení dat z script.js
            if (typeof window.employeesData !== 'undefined' && window.employeesData.length > 0) {
                employeesData = window.employeesData;
                console.log('Načtena data zaměstnanců:', employeesData.length);
                resolve();
            } else {
                // Pokud data nejsou k dispozici, zkusíme to za chvíli
                setTimeout(() => {
                    if (typeof window.employeesData !== 'undefined') {
                        employeesData = window.employeesData;
                        console.log('Načtena data zaměstnanců (opožděně):', employeesData.length);
                    }
                    resolve();
                }, 100);
            }
        });
    }

    // Detekce pohlaví ze jména
    function detectGender(name) {
        // Ženská jména obvykle končí na -á, -e nebo jsou to známá ženská jména
        const femaleEndings = ['á', 'e'];
        const femaleNames = ['Andrea', 'Jana', 'Eva', 'Hana', 'Lucie', 'Kateřina', 'Markéta', 'Veronika', 'Soňa'];

        const firstName = name.split(' ')[0];
        const lastName = name.split(' ')[1] || '';

        // Kontrola známých ženských jmen
        if (femaleNames.includes(firstName)) {
            return 'female';
        }

        // Kontrola koncovek příjmení
        if (femaleEndings.some(ending => lastName.endsWith(ending))) {
            return 'female';
        }

        return 'male';
    }

    // Načtení pozic markerů z mapa.html
    function loadMarkerPositions() {
        markerPositions = [
            {"jmeno": "Smrček Petr", "left": 954, "top": 610},
            {"jmeno": "Mičáň Alex", "left": 891, "top": 579},
            {"jmeno": "Vích Ondřej", "left": 1029, "top": 614},
            {"jmeno": "Tůma Tomáš", "left": 1140, "top": 616},
            {"jmeno": "Hrdá Veronika", "left": 1691, "top": 170},
            {"jmeno": "Vlčková Soňa", "left": 1691, "top": 211},
            {"jmeno": "Hons Jindřich", "left": 1607, "top": 170},
            {"jmeno": "Beáta Barošová", "left": 1560, "top": 169},
            {"jmeno": "Tomek Jiří", "left": 697, "top": 108},
            {"jmeno": "Zezuláková Andrea", "left": 697, "top": 143},
            {"jmeno": "Brzobohatá Jana", "left": 697, "top": 180},
            {"jmeno": "Haufenhoferová Eva", "left": 697, "top": 213},
            {"jmeno": "Kalábová Lucie", "left": 730, "top": 107},
            {"jmeno": "Mašková Hana", "left": 730, "top": 141},
            {"jmeno": "Prihara Roman", "left": 730, "top": 180},
            {"jmeno": "Laco Dušan", "left": 359, "top": 99},
            {"jmeno": "Šrom Jakub", "left": 461, "top": 114},
            {"jmeno": "Sharashenidze Akaki", "left": 647, "top": 173},
            {"jmeno": "Bok Zbyněk", "left": 647, "top": 100},
            {"jmeno": "Mareš Jan", "left": 647, "top": 135},
            {"jmeno": "Kohoutová Kateřina", "left": 595, "top": 140},
            {"jmeno": "Ryšavý Miroslav", "left": 595, "top": 104},
            {"jmeno": "Špala Jaroslav", "left": 575, "top": 158},
            {"jmeno": "Rýdl Zdeněk", "left": 522, "top": 121},
            {"jmeno": "Karas Karel", "left": 575, "top": 118},
            {"jmeno": "Hodánek Jaroslav", "left": 804, "top": 116},
            {"jmeno": "Kníže Jaromír", "left": 855, "top": 118},
            {"jmeno": "Pecánek Tomáš", "left": 855, "top": 151},
            {"jmeno": "Bílek Milan", "left": 855, "top": 187},
            {"jmeno": "Dvořák Tomáš", "left": 855, "top": 221},
            {"jmeno": "Kryštof Boháč", "left": 898, "top": 119},
            {"jmeno": "Knop Ondřej", "left": 894, "top": 157},
            {"jmeno": "Gregor Boris", "left": 894, "top": 199},
            {"jmeno": "Nečesaný Jakub", "left": 978, "top": 121},
            {"jmeno": "Srb Václav", "left": 1015, "top": 124},
            {"jmeno": "Šmídek Filip", "left": 1015, "top": 163},
            {"jmeno": "Láník Libor", "left": 1015, "top": 205},
            {"jmeno": "Jedličková Markéta", "left": 1075, "top": 124}
        ];
    }

    // Přepínání mezi sekcemi
    function switchToEmployees() {
        currentView = 'employees';
        
        // Aktualizace tlačítek
        employeeListBtn.classList.add('active');
        officeMapBtn.classList.remove('active');
        
        // Aktualizace titulku
        pageTitle.textContent = 'Seznam zaměstnanců';
        
        // Přepnutí sekcí s animací
        mapSection.style.display = 'none';
        employeeSection.style.display = 'block';
        
        // Animace
        employeeSection.classList.remove('section-transition');
        setTimeout(() => {
            employeeSection.classList.add('section-transition', 'active');
        }, 50);
    }

    function switchToMap() {
        currentView = 'map';
        
        // Aktualizace tlačítek
        officeMapBtn.classList.add('active');
        employeeListBtn.classList.remove('active');
        
        // Aktualizace titulku
        pageTitle.textContent = 'Mapa rozmístění pracovišť';
        
        // Přepnutí sekcí s animací
        employeeSection.style.display = 'none';
        mapSection.style.display = 'block';
        
        // Animace
        mapSection.classList.remove('section-transition');
        setTimeout(() => {
            mapSection.classList.add('section-transition', 'active');
            initializeMap();
        }, 50);
    }

    // Inicializace mapy
    function initializeMap() {
        if (currentView !== 'map') return;
        
        loadMarkerPositions();
        createEmployeeList();
        createMarkers();
        setupMapSearch();
        setupViewToggle();
    }

    // Vytvoření seznamu zaměstnanců pro mapu
    function createEmployeeList() {
        const employeeList = document.getElementById('mapEmployeeList');
        if (!employeeList) return;
        
        employeeList.innerHTML = '';
        
        // Filtrujeme pouze zaměstnance, kteří mají pozici na mapě
        const employeesWithPositions = employeesData.filter(emp => 
            markerPositions.some(pos => pos.jmeno === emp.jmeno)
        );
        
        employeesWithPositions.forEach(employee => {
            const li = document.createElement('li');
            li.innerHTML = `
                <img src="${employee.obrazek || 'img/no-person-photo.png'}" alt="${employee.jmeno}" class="avatar-img">
                <div class="employee-info">
                    <span class="emp-name">${employee.jmeno}</span>
                </div>
                <button class="find-btn" onclick="findEmployeeOnMap('${employee.jmeno}')">
                    <i class="fas fa-search"></i>
                    Najít
                </button>
            `;
            employeeList.appendChild(li);
        });
    }

    // Vytvoření markerů na mapě
    function createMarkers() {
        const mapContainer = document.querySelector('.office-map-container');
        if (!mapContainer) return;
        
        // Odstranění existujících markerů
        const existingMarkers = mapContainer.querySelectorAll('.marker');
        existingMarkers.forEach(marker => marker.remove());
        
        markerPositions.forEach(position => {
            const employee = employeesData.find(emp => emp.jmeno === position.jmeno);
            if (!employee) return;
            
            const marker = document.createElement('div');
            const gender = detectGender(employee.jmeno);
            marker.className = `marker ${gender}`;
            marker.style.left = position.left + 'px';
            marker.style.top = position.top + 'px';
            marker.setAttribute('data-employee', employee.jmeno);
            
            marker.innerHTML = `
                <img src="${employee.obrazek || 'img/no-person-photo.png'}" alt="${employee.jmeno}" class="marker-avatar">
                <div class="tooltip">${employee.jmeno}</div>
            `;
            
            // Přidání event listeneru pro kliknutí na marker
            marker.addEventListener('click', () => {
                showEmployeeDetails(employee);
            });
            
            mapContainer.appendChild(marker);
        });
    }

    // Vyhledávání na mapě
    function setupMapSearch() {
        const searchInput = document.getElementById('mapSearchInput');
        const clearBtn = document.getElementById('mapSearchClearBtn');
        const resultsCount = document.getElementById('mapSearchResultsCount');
        
        if (!searchInput) return;
        
        searchInput.addEventListener('input', function() {
            const query = this.value.toLowerCase().trim();
            
            if (query.length > 0) {
                clearBtn.classList.add('visible');
                filterEmployeesOnMap(query);
            } else {
                clearBtn.classList.remove('visible');
                showAllEmployeesOnMap();
            }
        });
        
        clearBtn.addEventListener('click', function() {
            searchInput.value = '';
            clearBtn.classList.remove('visible');
            showAllEmployeesOnMap();
            searchInput.focus();
        });
    }

    // Filtrování zaměstnanců na mapě
    function filterEmployeesOnMap(query) {
        const employeeItems = document.querySelectorAll('#mapEmployeeList li');
        const markers = document.querySelectorAll('.marker');
        let visibleCount = 0;
        
        employeeItems.forEach((item, index) => {
            const name = item.querySelector('.emp-name').textContent.toLowerCase();
            const marker = markers[index];
            
            if (name.includes(query)) {
                item.style.display = 'flex';
                if (marker) marker.style.display = 'flex';
                visibleCount++;
            } else {
                item.style.display = 'none';
                if (marker) marker.style.display = 'none';
            }
        });
        
        // Aktualizace počtu výsledků
        const resultsCount = document.getElementById('mapSearchResultsCount');
        const resultsText = document.getElementById('mapResultsCountText');
        if (resultsCount && resultsText) {
            resultsText.textContent = `${visibleCount} výsledků`;
            resultsCount.classList.add('visible');
        }
    }

    // Zobrazení všech zaměstnanců na mapě
    function showAllEmployeesOnMap() {
        const employeeItems = document.querySelectorAll('#mapEmployeeList li');
        const markers = document.querySelectorAll('.marker');
        const resultsCount = document.getElementById('mapSearchResultsCount');
        
        employeeItems.forEach(item => item.style.display = 'flex');
        markers.forEach(marker => marker.style.display = 'flex');
        
        if (resultsCount) {
            resultsCount.classList.remove('visible');
        }
    }

    // Nastavení toggle tlačítek pro zobrazení
    function setupViewToggle() {
        const toggleBtns = document.querySelectorAll('.toggle-btn');
        const mapContainer = document.querySelector('.office-map-container');
        
        toggleBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const view = this.getAttribute('data-view');
                
                // Aktualizace aktivního tlačítka
                toggleBtns.forEach(b => b.classList.remove('active'));
                this.classList.add('active');
                
                // Přepnutí režimu zobrazení
                if (view === 'single') {
                    mapContainer.classList.add('single-view');
                } else {
                    mapContainer.classList.remove('single-view');
                }
            });
        });
    }

    // Globální funkce pro vyhledání zaměstnance na mapě
    window.findEmployeeOnMap = function(employeeName) {
        const marker = document.querySelector(`[data-employee="${employeeName}"]`);
        if (marker) {
            // Zvýraznění markeru
            document.querySelectorAll('.marker').forEach(m => m.classList.remove('spotlight'));
            marker.classList.add('spotlight');
            
            // Scroll k markeru
            marker.scrollIntoView({ behavior: 'smooth', block: 'center' });
            
            // Zobrazení detailů zaměstnance
            const employee = employeesData.find(emp => emp.jmeno === employeeName);
            if (employee) {
                showEmployeeDetails(employee);
            }
        }
    };

    // Zobrazení detailů zaměstnance (použije existující modal)
    function showEmployeeDetails(employee) {
        // Použije existující modal z hlavní stránky
        if (typeof window.openModal === 'function') {
            window.openModal(employee);
        }
    }

    // Event listenery pro navigační tlačítka
    employeeListBtn.addEventListener('click', switchToEmployees);
    officeMapBtn.addEventListener('click', switchToMap);

    // Inicializace
    loadEmployeesData().then(() => {
        // Nastavení výchozího zobrazení
        switchToEmployees();
    });
});
