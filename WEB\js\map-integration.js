// Map Integration JavaScript - Global variables
let currentView = 'employees';
let mapInitialized = false;
// Note: employeesData is provided by script.js via window.employeesData

// Global functions for view switching - properly exposed to window object
function switchToEmployees() {
    console.log('Switching to employees view');
    currentView = 'employees';

    const employeeListBtn = document.getElementById('employeeListBtn');
    const officeMapBtn = document.getElementById('officeMapBtn');
    const employeeSection = document.getElementById('employeeSection');
    const mapSection = document.getElementById('mapSection');
    const pageTitle = document.getElementById('pageTitle');

    if (employeeListBtn && officeMapBtn && employeeSection && mapSection && pageTitle) {
        // Update buttons
        employeeListBtn.classList.add('active');
        officeMapBtn.classList.remove('active');

        // Update title
        pageTitle.textContent = 'Seznam zaměstnanců';

        // Switch sections
        mapSection.style.display = 'none';
        employeeSection.style.display = 'block';

        console.log('Successfully switched to employees view');
    } else {
        console.error('Some elements not found for employees view switch');
    }
}

function switchToMap() {
    console.log('Switching to map view');
    currentView = 'map';

    const employeeListBtn = document.getElementById('employeeListBtn');
    const officeMapBtn = document.getElementById('officeMapBtn');
    const employeeSection = document.getElementById('employeeSection');
    const mapSection = document.getElementById('mapSection');
    const pageTitle = document.getElementById('pageTitle');

    if (employeeListBtn && officeMapBtn && employeeSection && mapSection && pageTitle) {
        // Update buttons
        officeMapBtn.classList.add('active');
        employeeListBtn.classList.remove('active');

        // Update title
        pageTitle.textContent = 'Mapa rozmístění pracovišť';

        // Switch sections
        employeeSection.style.display = 'none';
        mapSection.style.display = 'block';

        // Initialize map on first display
        if (!mapInitialized) {
            console.log('Initializing map for first time');
            if (typeof window.initializeMap === 'function') {
                window.initializeMap();
                mapInitialized = true;
            } else {
                console.log('Waiting for map initialization...');
                // Wait for initializeMap to be available
                const checkInitialize = setInterval(() => {
                    if (typeof window.initializeMap === 'function') {
                        clearInterval(checkInitialize);
                        window.initializeMap();
                        mapInitialized = true;
                    }
                }, 50);
            }
        }

        console.log('Successfully switched to map view');
    } else {
        console.error('Some elements not found for map view switch');
    }
}

// Expose functions to global scope for onclick handlers
window.switchToEmployees = switchToEmployees;
window.switchToMap = switchToMap;

document.addEventListener('DOMContentLoaded', function() {
    console.log('Map integration script loaded!');
    console.log('window.employeesData available:', typeof window.employeesData !== 'undefined');
    if (typeof window.employeesData !== 'undefined') {
        console.log('window.employeesData length:', window.employeesData.length);
    }

    // Wait a bit for DOM to fully load
    setTimeout(function() {
        console.log('Initializing map integration...');
        initializeMapIntegration();
    }, 100);
});

function initializeMapIntegration() {
    console.log('initializeMapIntegration started');
    console.log('window.employeesData check:', typeof window.employeesData !== 'undefined');

    // Pozice markerů - kopie z mapa.html
    let markerPositions = [
        {"jmeno": "Smrček Petr", "left": 954, "top": 610},
        {"jmeno": "Mičáň Alex", "left": 891, "top": 579},
        {"jmeno": "Vích Ondřej", "left": 1029, "top": 614},
        {"jmeno": "Tůma Tomáš", "left": 1140, "top": 616},
        {"jmeno": "Hrdá Veronika", "left": 1691, "top": 170},
        {"jmeno": "Vlčková Soňa", "left": 1691, "top": 211},
        {"jmeno": "Hons Jindřich", "left": 1607, "top": 170},
        {"jmeno": "Beáta Barošová", "left": 1560, "top": 169},
        {"jmeno": "Tomek Jiří", "left": 697, "top": 108},
        {"jmeno": "Mareš Jan", "left": 647, "top": 135},
        {"jmeno": "Kohoutová Kateřina", "left": 595, "top": 140},
        {"jmeno": "Ryšavý Miroslav", "left": 595, "top": 104},
        {"jmeno": "Špala Jaroslav", "left": 575, "top": 158},
        {"jmeno": "Rýdl Zdeněk", "left": 522, "top": 121},
        {"jmeno": "Karas Karel", "left": 575, "top": 118},
        {"jmeno": "Hodánek Jaroslav", "left": 804, "top": 116},
        {"jmeno": "Kníže Jaromír", "left": 855, "top": 118},
        {"jmeno": "Pecánek Tomáš", "left": 855, "top": 151},
        {"jmeno": "Bílek Milan", "left": 855, "top": 187},
        {"jmeno": "Dvořák Tomáš", "left": 855, "top": 221},
        {"jmeno": "Kryštof Boháč", "left": 898, "top": 119},
        {"jmeno": "Knop Ondřej", "left": 894, "top": 157},
        {"jmeno": "Gregor Boris", "left": 894, "top": 199},
        {"jmeno": "Nečesaný Jakub", "left": 978, "top": 121},
        {"jmeno": "Srb Václav", "left": 1015, "top": 124},
        {"jmeno": "Šmídek Filip", "left": 1015, "top": 163},
        {"jmeno": "Láník Libor", "left": 1015, "top": 205},
        {"jmeno": "Jedličková Markéta", "left": 1075, "top": 126},
        {"jmeno": "Horová Žaneta", "left": 1075, "top": 164},
        {"jmeno": "Nohejlová Lucie", "left": 1075, "top": 205},
        {"jmeno": "Mesteková Alena", "left": 1135, "top": 126},
        {"jmeno": "Mácová Petra", "left": 1135, "top": 164},
        {"jmeno": "Mnuková Monika", "left": 1135, "top": 205},
        {"jmeno": "Zelenková Jana", "left": 1820, "top": 544},
        {"jmeno": "Jindrová Jiřina", "left": 1752, "top": 572},
        {"jmeno": "Váchalová Zuzana", "left": 1652, "top": 578},
        {"jmeno": "Nardelli Magdalena", "left": 1554, "top": 578},
        {"jmeno": "Soukupová Michaela", "left": 1605, "top": 578},
        {"jmeno": "Raška Michal", "left": 1320, "top": 607},
        {"jmeno": "Tepličanec Pavel", "left": 1212, "top": 612},
        {"jmeno": "Fridrichová Katarína", "left": 1212, "top": 578},
        {"jmeno": "Sojka Alena", "left": 1271, "top": 608},
        {"jmeno": "Záviský Ondřej", "left": 664, "top": 609},
        {"jmeno": "Čermák Martin", "left": 721, "top": 612},
        {"jmeno": "Lebeda Dušan", "left": 721, "top": 577},
        {"jmeno": "Hesko Martin", "left": 791, "top": 573},
        {"jmeno": "Houdek Ondřej", "left": 791, "top": 606},
        {"jmeno": "Novák Petr", "left": 440, "top": 572},
        {"jmeno": "Chemišinec Igor", "left": 400, "top": 612},
        {"jmeno": "Lobotková Alena", "left": 305, "top": 557},
        {"jmeno": "Puchel Michal", "left": 148, "top": 484},
        {"jmeno": "Vichrová Martina", "left": 75, "top": 358},
        {"jmeno": "Drdák Josef", "left": 72, "top": 304},
        {"jmeno": "Kreuzman Jiří", "left": 98, "top": 212},
        {"jmeno": "Kánský Jiří", "left": 1867, "top": 303},
        {"jmeno": "Valent Lajos", "left": 1867, "top": 360},
        {"jmeno": "Procházková Kateřina", "left": 1627, "top": 374},
        {"jmeno": "Kurfiřtová Pavla", "left": 1627, "top": 426},
        {"jmeno": "Bočánek Stanislav", "left": 894, "top": 130},
        {"jmeno": "Radka Maňurová", "left": 1871, "top": 226},
        {"jmeno": "Sylvie Karolová", "left": 1823, "top": 333},
        {"jmeno": "František Černý", "left": 1085, "top": 616}
    ];

    // Load employee data from global variable
    function loadEmployeesData() {
        return new Promise((resolve) => {
            // Wait for data to be loaded from script.js
            if (typeof window.employeesData !== 'undefined' && window.employeesData.length > 0) {
                console.log('Employee data loaded:', window.employeesData.length, 'employees');
                resolve();
            } else {
                // If data is not available, try again after a short delay
                setTimeout(() => {
                    if (typeof window.employeesData !== 'undefined') {
                        console.log('Employee data loaded (delayed):', window.employeesData.length, 'employees');
                    } else {
                        console.warn('Employee data still not available');
                    }
                    resolve();
                }, 100);
            }
        });
    }

    // Funkce pro normalizaci jmen
    function normalizeName(str) {
        return str
            .normalize('NFD')
            .replace(/\p{Diacritic}/gu, '')
            .toLowerCase()
            .replace(/\s+/g, ' ')
            .trim();
    }

    // Detekce pohlaví podle jména - kopie z mapa.html
    function detectGender(fullName) {
        const nameParts = fullName.split(' ');
        const firstName = nameParts[nameParts.length - 1].toLowerCase();

        const femaleNames = [
            'alena', 'andrea', 'beáta', 'beata', 'eva', 'hana', 'helena', 'jana', 'jiřina', 'jirina',
            'kateřina', 'katerina', 'katarína', 'katarina', 'lucie', 'magdalena', 'markéta', 'marketa',
            'martina', 'michaela', 'monika', 'pavla', 'petra', 'soňa', 'sona', 'veronika', 'zuzana',
            'žaneta', 'zaneta', 'alice', 'varvara', 'magda', 'katarína', 'fridrichová', 'staňková',
            'stašková', 'soukupová', 'procházková', 'kurfiřtová', 'vachalová', 'erhartová', 'hulová',
            'jedličková', 'horová', 'nohejlová', 'mesteková', 'mácová', 'mnuková', 'nardelli', 'zelenková',
            'zezuľáková', 'karolová', 'maňurová', 'vichrová', 'vlčková', 'hrdá', 'gabriel', 'kopecká',
            'sojka', 'pešková'
        ];

        const hasFemaleLastName = fullName.toLowerCase().includes('ová') ||
            (fullName.toLowerCase().endsWith('ina') && !fullName.toLowerCase().includes('ák') && !fullName.toLowerCase().includes('ak'));

        const specialFemaleNames = [
            'vasjuňkina varvara', 'hrdá veronika', 'gabriel martina', 'kopecká zuzana',
            'nardelli magdalena', 'sojka alena', 'pešková monika'
        ];

        const isSpecialFemale = specialFemaleNames.includes(fullName.toLowerCase());

        const maleNames = [
            'petr', 'tomáš', 'tomas', 'ondřej', 'ondrej', 'jakub', 'jindřich', 'jindrich', 'jaroslav',
            'boris', 'libor', 'václav', 'vaclav', 'filip', 'vojtěch', 'vojtech', 'pavel', 'michal',
            'karel', 'jaromír', 'jaromir', 'roman', 'miroslav', 'zdeněk', 'zdenek', 'dušan', 'dusan',
            'josef', 'jiří', 'jiri', 'stanislav', 'igor', 'akaki', 'zbyněk', 'zbyněk', 'kryštof',
            'krystof', 'martin', 'ondřej', 'ondrej', 'pavel', 'lajos', 'františek', 'frantisek'
        ];

        const isMale = maleNames.includes(firstName);
        const isFemale = femaleNames.includes(firstName) || hasFemaleLastName || isSpecialFemale;

        if (isMale) {
            return 'male';
        } else if (isFemale) {
            return 'female';
        } else {
            return 'male'; // default
        }
    }

    // Načtení pozic markerů z mapa.html
    function loadMarkerPositions() {
        markerPositions = [
            {"jmeno": "Smrček Petr", "left": 954, "top": 610},
            {"jmeno": "Mičáň Alex", "left": 891, "top": 579},
            {"jmeno": "Vích Ondřej", "left": 1029, "top": 614},
            {"jmeno": "Tůma Tomáš", "left": 1140, "top": 616},
            {"jmeno": "Hrdá Veronika", "left": 1691, "top": 170},
            {"jmeno": "Vlčková Soňa", "left": 1691, "top": 211},
            {"jmeno": "Hons Jindřich", "left": 1607, "top": 170},
            {"jmeno": "Beáta Barošová", "left": 1560, "top": 169},
            {"jmeno": "Tomek Jiří", "left": 697, "top": 108},
            {"jmeno": "Zezuláková Andrea", "left": 697, "top": 143},
            {"jmeno": "Brzobohatá Jana", "left": 697, "top": 180},
            {"jmeno": "Haufenhoferová Eva", "left": 697, "top": 213},
            {"jmeno": "Kalábová Lucie", "left": 730, "top": 107},
            {"jmeno": "Mašková Hana", "left": 730, "top": 141},
            {"jmeno": "Prihara Roman", "left": 730, "top": 180},
            {"jmeno": "Laco Dušan", "left": 359, "top": 99},
            {"jmeno": "Šrom Jakub", "left": 461, "top": 114},
            {"jmeno": "Sharashenidze Akaki", "left": 647, "top": 173},
            {"jmeno": "Bok Zbyněk", "left": 647, "top": 100},
            {"jmeno": "Mareš Jan", "left": 647, "top": 135},
            {"jmeno": "Kohoutová Kateřina", "left": 595, "top": 140},
            {"jmeno": "Ryšavý Miroslav", "left": 595, "top": 104},
            {"jmeno": "Špala Jaroslav", "left": 575, "top": 158},
            {"jmeno": "Rýdl Zdeněk", "left": 522, "top": 121},
            {"jmeno": "Karas Karel", "left": 575, "top": 118},
            {"jmeno": "Hodánek Jaroslav", "left": 804, "top": 116},
            {"jmeno": "Kníže Jaromír", "left": 855, "top": 118},
            {"jmeno": "Pecánek Tomáš", "left": 855, "top": 151},
            {"jmeno": "Bílek Milan", "left": 855, "top": 187},
            {"jmeno": "Dvořák Tomáš", "left": 855, "top": 221},
            {"jmeno": "Kryštof Boháč", "left": 898, "top": 119},
            {"jmeno": "Knop Ondřej", "left": 894, "top": 157},
            {"jmeno": "Gregor Boris", "left": 894, "top": 199},
            {"jmeno": "Nečesaný Jakub", "left": 978, "top": 121},
            {"jmeno": "Srb Václav", "left": 1015, "top": 124},
            {"jmeno": "Šmídek Filip", "left": 1015, "top": 163},
            {"jmeno": "Láník Libor", "left": 1015, "top": 205},
            {"jmeno": "Jedličková Markéta", "left": 1075, "top": 124}
        ];
    }



    // Inicializace mapy
    function initializeMap() {
        console.log('Inicializace mapy...');
        loadMarkerPositions();
        createEmployeeList();
        createMarkers();
        setupMapSearch();
        setupMapToggle();

        // Kontrola načtení obrázku
        const mapImg = document.getElementById('office-map-img');
        const errorDiv = document.getElementById('map-error');

        mapImg.onload = function() {
            errorDiv.style.display = 'none';
        };

        mapImg.onerror = function() {
            errorDiv.style.display = 'block';
        };
    }

    // Make initializeMap globally accessible
    window.initializeMap = initializeMap;

    // Vytvoření horizontálního seznamu zaměstnanců - kopie z mapa.html
    function createEmployeeList() {
        const employeeList = document.getElementById('mapEmployeeList');
        if (!employeeList) return;

        employeeList.innerHTML = '';

        // Show ALL employees like in original mapa.html, not just those with positions
        const allEmployees = [...window.employeesData];

        // Sort employees alphabetically like in original
        allEmployees.sort((a, b) => {
            const nameA = normalizeName(a.jmeno.trim().toUpperCase());
            const nameB = normalizeName(b.jmeno.trim().toUpperCase());
            return nameA.localeCompare(nameB, 'cs');
        });

        console.log('Zobrazuji všechny zaměstnance:', allEmployees.length);
        console.log('Pozice na mapě:', markerPositions.length);

        allEmployees.forEach(employee => {
            const li = document.createElement('li');
            li.dataset.jmeno = employee.jmeno;

            // Check if employee has a position on the map
            const hasMapPosition = markerPositions.some(pos =>
                normalizeName(pos.jmeno) === normalizeName(employee.jmeno)
            );

            li.innerHTML = `
                <img src="${employee.obrazek || 'img/no-person-photo.png'}" alt="${employee.jmeno}" class="avatar-img">
                <div class="employee-info">
                    <span class="emp-name">${employee.jmeno}</span>
                    ${!hasMapPosition ? '<span class="no-map-position" title="Pozice na mapě není k dispozici">📍</span>' : ''}
                </div>
                <button class="find-btn" onclick="findEmployeeOnMap('${employee.jmeno}')" ${!hasMapPosition ? 'disabled title="Pozice na mapě není k dispozici"' : ''}>
                    <i class="fas fa-search"></i>
                    Najít
                </button>
            `;

            employeeList.appendChild(li);
        });
    }

    // Vytvoření markerů na mapě - kopie z mapa.html
    function createMarkers() {
        const mapContainer = document.querySelector('.office-map-container');
        if (!mapContainer) {
            console.error('Map container not found');
            return;
        }

        // Odstranění existujících markerů
        const existingMarkers = mapContainer.querySelectorAll('.marker');
        existingMarkers.forEach(marker => marker.remove());

        console.log('Vytváření markerů pro', markerPositions.length, 'pozic');
        console.log('Map container found:', mapContainer);

        markerPositions.forEach(position => {
            const employee = window.employeesData.find(emp => normalizeName(emp.jmeno) === normalizeName(position.jmeno));
            if (!employee) {
                console.log('Employee not found for position:', position.jmeno);
                return;
            }

            const marker = document.createElement('div');
            const isFemaleName = detectGender(employee.jmeno) === 'female';
            marker.className = `marker ${isFemaleName ? 'female' : 'male'}`;
            marker.style.left = position.left + 'px';
            marker.style.top = position.top + 'px';
            marker.dataset.jmeno = employee.jmeno;
            marker.tabIndex = 0;

            // Create gender-colored dot (like in original mapa.html)
            const genderDot = document.createElement('div');
            genderDot.style.width = '26px';
            genderDot.style.height = '26px';
            genderDot.style.borderRadius = '50%';
            genderDot.style.background = isFemaleName ? '#ec4899' : '#3b82f6';
            genderDot.style.boxShadow = isFemaleName ?
                '0 2px 8px rgba(236, 72, 153, 0.3)' :
                '0 2px 8px rgba(59, 130, 246, 0.3)';
            genderDot.style.border = '2.5px solid #fff';
            genderDot.style.display = 'block';
            genderDot.style.transition = 'all 0.3s ease';
            genderDot.title = `${employee.jmeno} (${isFemaleName ? 'Žena' : 'Muž'})`;
            marker.appendChild(genderDot);

            // Create tooltip
            const tooltip = document.createElement('span');
            tooltip.className = 'tooltip';
            tooltip.innerHTML = employee.jmeno;
            marker.appendChild(tooltip);

            // Event listener pro kliknutí na marker
            marker.addEventListener('click', () => {
                highlightEmployeeOnMap(employee.jmeno);
                showEmployeeDetails(employee);
            });

            mapContainer.appendChild(marker);
            console.log('Marker created for:', employee.jmeno, 'at position:', position.left, position.top);
        });

        console.log('Total markers created:', mapContainer.querySelectorAll('.marker').length);
    }

    // Zvýraznění zaměstnance na mapě - kopie z mapa.html
    function highlightEmployeeOnMap(employeeName) {
        // Odstranění předchozího zvýraznění
        document.querySelectorAll('#mapEmployeeList li').forEach(li => li.classList.remove('active'));
        document.querySelectorAll('.marker').forEach(marker => {
            marker.classList.remove('active', 'spotlight');
        });

        // Zvýraznění v seznamu
        const employeeItem = document.querySelector(`#mapEmployeeList li[data-jmeno="${employeeName}"]`);
        if (employeeItem) {
            employeeItem.classList.add('active');
            employeeItem.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }

        // Zvýraznění markeru
        const marker = document.querySelector(`.marker[data-jmeno="${employeeName}"]`);
        if (marker) {
            marker.classList.add('active', 'spotlight');
        }
    }

    // Vyhledávání na mapě - kopie z mapa.html
    function setupMapSearch() {
        const searchInput = document.getElementById('mapSearchInput');
        const clearBtn = document.getElementById('mapSearchClearBtn');
        const resultsCount = document.getElementById('mapSearchResultsCount');

        if (!searchInput || !clearBtn) return;

        searchInput.addEventListener('input', function() {
            const query = this.value.toLowerCase().trim();

            if (query.length > 0) {
                clearBtn.classList.add('visible');
                const count = filterEmployeesOnMap(query);
                if (resultsCount) {
                    const resultsText = document.getElementById('mapResultsCountText');
                    if (resultsText) {
                        resultsText.textContent = `${count} výsledků`;
                    }
                    resultsCount.classList.add('visible');
                }
            } else {
                clearBtn.classList.remove('visible');
                showAllEmployeesOnMap();
                if (resultsCount) {
                    resultsCount.classList.remove('visible');
                }
            }
        });

        clearBtn.addEventListener('click', function() {
            searchInput.value = '';
            clearBtn.classList.remove('visible');
            showAllEmployeesOnMap();
            if (resultsCount) {
                resultsCount.classList.remove('visible');
            }
            searchInput.focus();
        });
    }

    // Filtrování zaměstnanců na mapě - kopie z mapa.html
    function filterEmployeesOnMap(query) {
        const employeeItems = document.querySelectorAll('#mapEmployeeList li');
        let visibleCount = 0;

        employeeItems.forEach(item => {
            const name = item.querySelector('.emp-name').textContent.toLowerCase();
            const employeeName = item.dataset.jmeno;
            const marker = document.querySelector(`.marker[data-jmeno="${employeeName}"]`);

            if (normalizeName(name).includes(normalizeName(query))) {
                item.style.display = 'flex';
                if (marker) marker.style.display = 'flex';
                visibleCount++;
            } else {
                item.style.display = 'none';
                if (marker) marker.style.display = 'none';
            }
        });

        console.log(`Nalezeno ${visibleCount} výsledků pro "${query}"`);
        return visibleCount;
    }

    // Zobrazení všech zaměstnanců na mapě
    function showAllEmployeesOnMap() {
        const employeeItems = document.querySelectorAll('#mapEmployeeList li');
        const markers = document.querySelectorAll('.marker');

        employeeItems.forEach(item => item.style.display = 'flex');
        markers.forEach(marker => marker.style.display = 'flex');
    }

    // Nastavení toggle tlačítek pro zobrazení - kopie z mapa.html
    function setupMapToggle() {
        const toggleBtns = document.querySelectorAll('.toggle-btn');
        const mapContainer = document.querySelector('.office-map-container');

        if (!toggleBtns.length || !mapContainer) {
            console.log('Toggle buttons or map container not found');
            return;
        }

        toggleBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const view = this.getAttribute('data-view');

                // Aktualizace aktivního tlačítka
                toggleBtns.forEach(b => b.classList.remove('active'));
                this.classList.add('active');

                // Přepnutí režimu zobrazení
                if (view === 'single') {
                    mapContainer.classList.add('single-view');
                } else {
                    mapContainer.classList.remove('single-view');
                }

                console.log('Přepnuto na režim:', view);
            });
        });
    }

    // Globální funkce pro vyhledání zaměstnance na mapě - kopie z mapa.html
    window.findEmployeeOnMap = function(employeeName) {
        if (currentView !== 'map') {
            switchToMap();
            setTimeout(() => findEmployeeOnMap(employeeName), 500);
            return;
        }

        // Check if employee has a position on the map
        const hasMapPosition = markerPositions.some(pos =>
            normalizeName(pos.jmeno) === normalizeName(employeeName)
        );

        if (!hasMapPosition) {
            alert(`Zaměstnanec "${employeeName}" nemá pozici na mapě.`);
            return;
        }

        highlightEmployeeOnMap(employeeName);

        const employee = window.employeesData.find(emp => normalizeName(emp.jmeno) === normalizeName(employeeName));
        if (employee) {
            showEmployeeDetails(employee);
        }
    };

    // Zobrazení detailů zaměstnance (použije existující modal)
    function showEmployeeDetails(employee) {
        // Použije existující modal z hlavní stránky
        if (typeof window.openModal === 'function') {
            window.openModal(employee);
        }
    }

    // Navigační tlačítka nyní používají onclick atributy

    // Inicializace
    loadEmployeesData().then(() => {
        console.log('Data načtena, inicializace dokončena');
        // Nastavení výchozího zobrazení
        window.switchToEmployees();
    });
}
