<!DOCTYPE html>
<html lang="cs">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Seznam zaměstnanců OTE">
    <meta name="author" content="<PERSON>">
    <link rel="shortcut icon" href="img/favicon.ico" type="image/x-icon">
    <title>Seznam zaměstnanců</title>
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@200;300;400;600;700;800;900&display=swap"
        rel="stylesheet">
    <script src="https://kit.fontawesome.com/14940b9e28.js" crossorigin="anonymous"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/directory.css">
    <style>
        /* Navigační tlačítka */
        .navigation-buttons {
            display: flex;
            gap: 0.5rem;
            margin: 0 1rem;
        }

        .nav-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.25rem;
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(10px);
            text-decoration: none;
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.4);
            color: #fff;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .nav-btn.active {
            background: rgba(255, 255, 255, 0.95);
            border-color: rgba(255, 255, 255, 0.8);
            color: #2563eb;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .nav-btn i {
            font-size: 1.1rem;
        }

        /* Responzivní design pro navigaci */
        @media (max-width: 768px) {
            .navigation-buttons {
                margin: 0 0.5rem;
                gap: 0.25rem;
            }

            .nav-btn {
                padding: 0.5rem 0.75rem;
                font-size: 0.8rem;
            }

            .nav-btn span {
                display: none;
            }

            .nav-btn i {
                font-size: 1.2rem;
            }
        }

        /* Mapa sekce styly */
        .office_map_section {
            padding: 2rem 0;
            background: #f7f9fb;
            min-height: calc(100vh - 200px);
        }

        /* Přechody mezi sekcemi */
        .section-transition {
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .section-transition.active {
            opacity: 1;
            transform: translateY(0);
        }

        /* Styly z mapa.html */
        body {
            font-family: 'Nunito', Arial, sans-serif;
        }

        .main-flex {
            display: flex;
            flex-direction: row;
            align-items: flex-start;
            justify-content: flex-start;
            width: 100vw;
            max-width: none;
            margin: 2rem 0 2rem 0;
            gap: 48px;
        }

        .main-flex-custom {
            flex-direction: column;
            gap: 32px;
            align-items: stretch;
        }

        .employee-bar {
            width: 100%;
            margin: 0 0 1.5rem 0;
            background: linear-gradient(135deg, #ffffff 0%, #f8faff 100%);
            box-shadow:
                0 8px 24px rgba(37,99,235,0.06),
                0 4px 8px rgba(37,99,235,0.04),
                inset 0 1px 0 rgba(255,255,255,0.8);
            border-radius: 20px;
            border: 1px solid rgba(37,99,235,0.1);
            padding: 1.25rem 1.5rem;
            position: relative;
            z-index: 10;
            backdrop-filter: blur(10px);
            height: auto;
            min-height: 120px;
        }

        .search-header {
            display: flex;
            align-items: center;
            gap: 3rem;
            padding: 1rem 1.5rem;
            background: rgba(255,255,255,0.7);
            border-radius: 16px;
            border: 1px solid rgba(37,99,235,0.08);
            backdrop-filter: blur(20px);
            height: 80px;
        }

        .search-section {
            display: flex;
            align-items: center;
            flex-shrink: 0;
            min-width: 220px;
        }

        .search-row {
            display: flex;
            align-items: center;
            gap: 1rem;
            width: 100%;
        }

        .search-left {
            display: flex;
            align-items: center;
            gap: 1rem;
            flex: 1;
        }

        .search-container {
            position: relative;
            width: 200px;
            flex-shrink: 0;
        }

        .search-container::before {
            content: '\f002';
            font-family: 'Font Awesome 5 Free';
            font-weight: 900;
            position: absolute;
            left: 0.75rem;
            top: 50%;
            transform: translateY(-50%);
            color: #6b7280;
            font-size: 0.875rem;
            z-index: 1;
            pointer-events: none;
        }

        .search-box-horizontal {
            width: 100%;
            border-radius: 10px;
            border: 2px solid rgba(37,99,235,0.12);
            padding: 0.5rem 2.25rem 0.5rem 2rem;
            font-size: 0.85rem;
            background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(248,250,255,0.95) 100%);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            outline: none;
            color: #1f2937;
            font-weight: 500;
            box-shadow:
                0 4px 12px rgba(37,99,235,0.08),
                inset 0 1px 0 rgba(255,255,255,0.9);
            backdrop-filter: blur(12px);
            height: 36px;
            position: relative;
        }

        .search-box-horizontal:focus {
            border-color: #2563eb;
            background: linear-gradient(135deg, rgba(255,255,255,1) 0%, rgba(248,250,255,1) 100%);
            box-shadow:
                0 8px 32px rgba(37,99,235,0.15),
                0 0 0 3px rgba(37,99,235,0.1),
                inset 0 1px 0 rgba(255,255,255,1);
            transform: translateY(-1px);
        }

        .search-clear-btn {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(107,114,128,0.1);
            border: none;
            color: #6b7280;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 8px;
            transition: all 0.3s ease;
            opacity: 0;
            pointer-events: none;
            width: 2rem;
            height: 2rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .search-clear-btn.visible {
            opacity: 1;
            pointer-events: auto;
        }

        .search-clear-btn:hover {
            color: #dc2626;
            background: rgba(220,38,38,0.1);
            transform: translateY(-50%) scale(1.1);
        }

        .search-results-count {
            display: none;
            font-size: 0.75rem;
            color: #6b7280;
            font-weight: 500;
            padding: 0.25rem 0.5rem;
            background: rgba(37,99,235,0.08);
            border-radius: 8px;
            margin-left: 0.5rem;
            white-space: nowrap;
            transition: all 0.3s ease;
        }

        .search-results-count.visible {
            display: flex;
            align-items: center;
        }
    </style>


</head>

<body>

    <header id="headerNav">
        <div class="navbar">
            <div class="logo">
                <a href="index.html"><img src="img/logo1.svg" alt="Logo OTE" class="logo1"></a>
            </div>
            <h1 class="header-title" id="pageTitle">Seznam zaměstnanců</h1>
            <div class="navigation-buttons">
                <button id="employeeListBtn" class="nav-btn active" title="Seznam zaměstnanců">
                    <i class="fas fa-users"></i>
                    <span>Seznam zaměstnanců</span>
                </button>
                <button id="officeMapBtn" class="nav-btn" title="Mapa rozmístění pracovišť">
                    <i class="fas fa-map-marked-alt"></i>
                    <span>Mapa rozmístění pracovišť</span>
                </button>
            </div>
            <div class="search-container">
                <form action="#" class="search_form">
                    <input type="text" name="employee search" id="searchEmployee" placeholder="🔍 Vyhledat zaměstnance...">
                    <i class="fas fa-search" id="searchBtn"></i>
                    <div id="search-suggestions" class="search-suggestions"></div>
                </form>
            </div>
            <div class="theme-switch-wrapper">
                <label class="theme-switch" for="checkbox">
                    <input type="checkbox" id="checkbox" />
                    <div class="slider round">
                        <i class="fas fa-sun"></i>
                        <i class="fas fa-moon"></i>
                    </div>
                </label>
            </div>
        </div>
    </header>

    <!-- Seznam zaměstnanců -->
    <section class="employee_directory" id="employeeSection">
        <div class="filter-header">
        </div>

        <div class="department-filters" id="department-filters">
        </div>

        <div class="employee_listing" id="employee-grid">
        </div>
    </section>

    <!-- Mapa rozmístění pracovišť -->
    <section class="office_map_section" id="mapSection" style="display: none;">
        <div class="main-flex main-flex-custom">
            <div class="employee-bar">
                <div class="search-header">
                    <div class="search-section">
                        <div class="search-row">
                            <div class="search-left">
                                <div class="search-container">
                                    <input class="search-box-horizontal" type="text" id="mapSearchInput" placeholder="Vyhledat zaměstnance...">
                                    <button type="button" class="search-clear-btn" id="mapSearchClearBtn" title="Vymazat vyhledávání">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                                <div class="search-results-count" id="mapSearchResultsCount">
                                    <span id="mapResultsCountText">0 výsledků</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <ul class="employee-list-horizontal" id="mapEmployeeList"></ul>
                </div>
            </div>

            <div class="mapa-wrapper mapa-wrapper-custom">
                <div class="office-map-container">
                    <img id="office-map-img" src="img/Greenline.png" alt="Plán kanceláře">

                    <div class="view-toggle-floating">
                        <div class="view-toggle-compact">
                            <div class="toggle-header">Režim zobrazení</div>
                            <div class="toggle-buttons">
                                <button type="button" class="toggle-btn active" data-view="all" title="Zobrazit všechny zaměstnance na mapě">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zm4 18v-6h2.5l-2.54-7.63A1.5 1.5 0 0 0 18.54 8H17c-.8 0-1.54.37-2.01 1l-2.99 4v7h2v7h4zm-7.5-10.5c.83 0 1.5-.67 1.5-1.5s-.67-1.5-1.5-1.5S11 9.17 11 10.5s.67 1.5 1.5 1.5zM5.5 6c1.11 0 2-.89 2-2s-.89-2-2-2-2 .89-2 2 .89 2 2 2zm1.5 2h-2C3.34 8 2 9.34 2 11.5V22h2v-7h2v7h2v-10.5C8 9.34 6.66 8 5 8z"/>
                                    </svg>
                                    <span>Všichni</span>
                                </button>
                                <button type="button" class="toggle-btn" data-view="single" title="Zobrazit pouze vybraného zaměstnance">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                                    </svg>
                                    <span>Jednotlivec</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div id="img-error" class="error-message">Obrázek plánu kanceláře se nepodařilo načíst. Zkontrolujte cestu k souboru <b>img/Greenline.png</b> nebo kontaktujte správce.</div>
    </section>

    <footer id="Footer">
        <p>Poslední aktualizace: <span id="lastUpdated"></span></p>
    </footer>

    <button id="backToTopBtn" title="Zpět nahoru">
        <i class="fas fa-chevron-up"></i>
        <span>Nahoru</span>
    </button>

    <div id="employeeModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <img id="modalImage" src="" alt="Employee Image">
            <h2 id="modalName"></h2>
            <div class="modal-header">
                <div class="modal-info-group">
                    <p id="modalPosition"></p>
                    <p id="modalDepartment"></p>
                    <p id="modalOffice"></p>
                </div>
            </div>
            <p id="modalDescription"></p>
            <div class="modal-info">
                <div class="contact-item">
                    <i class="fas fa-phone icon"></i>
                    <span id="modalPhone">Telefon: Neuvedeno</span>
                </div>
                <div class="contact-item">
                    <i class="fas fa-mobile-alt icon"></i>
                    <span id="modalMobile">Mobil: Neuvedeno</span>
                </div>
                <div class="contact-item">
                    <i class="fas fa-envelope icon"></i>
                    <span id="modalEmail">Email: Neuvedeno</span>
                </div>
                <div id="modalTeams"></div>
            </div>
        </div>
    </div>

    <script src="js/navbar.js"></script>
    <script src="js/script.js"></script>
    <script src="js/back-to-top.js"></script>
    <script src="js/update-date.js"></script>

    <script>
        document.addEventListener('contextmenu', function (e) {
            e.preventDefault();
        });
    </script>

</body>

</html>
